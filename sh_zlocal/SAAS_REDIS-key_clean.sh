#!/bin/bash

# =============================================================================
# Redis Key清理脚本
# 功能：根据指定的service_id前缀批量删除Redis中的key
# 作者：自动化运维脚本
# 版本：1.0
# =============================================================================

# ===
# ssh u125r
# cd /user/sqldata
# ===

set -eu  # 严格模式：遇到错误立即退出
# 注意：某些shell不支持pipefail选项，为了兼容性这里不使用

# =============================================================================
# 全局变量配置
# =============================================================================

# Redis连接配置
REDIS_HOST="${REDIS_HOST:-**************}"          # Redis主机地址
REDIS_PORT="${REDIS_PORT:-63790}"               # Redis端口
REDIS_PASSWORD="${REDIS_PASSWORD:-}"            # Redis密码（可选）
REDIS_DB="${REDIS_DB:-0}"                      # Redis数据库编号
SERVICE_IDS=""                                  # service_id前缀列表（逗号分隔）

# 脚本配置
SCRIPT_NAME=$(basename "$0")
LOG_LEVEL="INFO"                         # 日志级别
DRY_RUN=false                            # 是否为试运行模式
BATCH_SIZE=1000                          # 批量删除大小

# 颜色配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# 日志函数
# =============================================================================

# 记录信息日志
log_info() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1" >&2
}

# 记录警告日志
log_warn() {
    printf "${YELLOW}[WARN]${NC} %s\n" "$1" >&2
}

# 记录错误日志
log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1" >&2
}

# 记录成功日志
log_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1" >&2
}

# =============================================================================
# 工具函数
# =============================================================================

# 显示帮助信息
show_help() {
    cat << EOF
Redis Key清理脚本

使用方法:
  $SCRIPT_NAME [OPTIONS] SERVICE_IDS

参数说明:
  SERVICE_IDS        - service_id前缀，支持多个前缀用英文逗号分隔

选项:
  -h, --help         显示此帮助信息
  -d, --dry-run      试运行模式，只显示将要删除的key，不实际删除
  -H, --host HOST    Redis主机地址 (默认: 127.0.0.1)
  -P, --port PORT    Redis端口 (默认: 6379)
  -a, --auth PWD     Redis密码
  -n, --db DB        Redis数据库编号 (默认: 0)
  -b, --batch SIZE   批量删除大小 (默认: 1000)

环境变量:
  REDIS_HOST         Redis主机地址
  REDIS_PORT         Redis端口
  REDIS_PASSWORD     Redis密码
  REDIS_DB           Redis数据库编号

示例:
  $SCRIPT_NAME "svc001,svc002,svc003"
  $SCRIPT_NAME -H ************* -a mypassword "app1,app2"
  $SCRIPT_NAME --dry-run "temp,backup"

EOF
}

# 检查依赖
check_dependencies() {
    # 检查redis-cli命令
    if ! command -v redis-cli >/dev/null 2>&1; then
        log_error "缺少必要的依赖: redis-cli"
        log_error "请安装Redis客户端工具"
        exit 1
    fi
}

# =============================================================================
# Redis操作函数
# =============================================================================

# 构建Redis命令
build_redis_cmd() {
    local redis_cmd="redis-cli -h $REDIS_HOST -p $REDIS_PORT -n $REDIS_DB"
    
    if [ -n "$REDIS_PASSWORD" ]; then
        redis_cmd="$redis_cmd -a $REDIS_PASSWORD"
    fi
    
    echo "$redis_cmd"
}

# 测试Redis连接
test_redis_connection() {
    log_info "测试Redis连接: $REDIS_HOST:$REDIS_PORT (DB: $REDIS_DB)"
    
    local redis_cmd
    redis_cmd=$(build_redis_cmd)
    
    if $redis_cmd ping >/dev/null 2>&1; then
        log_success "Redis连接测试成功"
        return 0
    else
        log_error "无法连接到Redis服务器"
        log_error "请检查Redis地址、端口、密码和数据库编号是否正确"
        return 1
    fi
}

# 获取匹配前缀的Redis key列表
get_matching_keys() {
    log_info "查找前缀为 '$SERVICE_IDS' 的Redis key..." >&2
    
    local redis_cmd
    redis_cmd=$(build_redis_cmd)
    
    # 创建临时文件存储匹配的key
    local temp_file
    temp_file=$(mktemp)
    local key_count=0
    
    # 处理逗号分隔的SERVICE_IDS
    # 使用here document避免子shell问题
    while IFS= read -r service_id; do
        # 去除前缀两端的空格
        service_id=$(echo "$service_id" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
        
        if [ -n "$service_id" ]; then
            # 使用SCAN命令查找匹配的key，避免使用KEYS命令阻塞Redis
            $redis_cmd --scan --pattern "${service_id}*" 2>/dev/null || true
        fi
    done << EOF > "$temp_file"
$(echo "$SERVICE_IDS" | tr ',' '\n')
EOF
    
    # 检查是否找到匹配的key
    key_count=$(wc -l < "$temp_file" | tr -d ' ')
    
    if [ "$key_count" -eq 0 ]; then
        log_warn "未找到前缀为 '$SERVICE_IDS' 的Redis key" >&2
        rm -f "$temp_file"
        return 1
    fi
    
    log_info "找到 $key_count 个匹配的key" >&2
    
    # 输出匹配的key并清理临时文件
    cat "$temp_file"
    rm -f "$temp_file"
}

# 删除Redis key
delete_keys() {
    # 从标准输入读取key列表
    local temp_file
    temp_file=$(mktemp)
    cat > "$temp_file"
    
    local total_keys
    total_keys=$(wc -l < "$temp_file" | tr -d ' ')
    
    if [ "$total_keys" -eq 0 ]; then
        rm -f "$temp_file"
        return 0
    fi
    
    log_warn "即将删除 $total_keys 个Redis key，此操作不可逆！" >&2
    
    if [ "$DRY_RUN" = true ]; then
        log_info "试运行模式：以下key将被删除（实际不会删除）:" >&2
        local count=0
        while IFS= read -r key && [ "$count" -lt 20 ]; do
            log_info "  - $key" >&2
            count=$((count + 1))
        done < "$temp_file"
        
        if [ "$total_keys" -gt 20 ]; then
            log_info "  ... 还有 $((total_keys - 20)) 个key" >&2
        fi
        
        rm -f "$temp_file"
        return 0
    fi
    
    # 确认删除
    printf "确认删除以上Redis key？(y/N): " >&2
    read -r confirmation < /dev/tty
    if [ "$confirmation" != "y" ] && [ "$confirmation" != "Y" ]; then
        log_info "操作已取消" >&2
        rm -f "$temp_file"
        return 0
    fi
    
    local redis_cmd
    redis_cmd=$(build_redis_cmd)
    
    # 批量删除key
    log_info "开始批量删除Redis key..." >&2
    
    local success_count=0
    local fail_count=0
    local processed=0
    local batch_temp
    batch_temp=$(mktemp)
    
    while IFS= read -r key; do
        echo "$key" >> "$batch_temp"
        processed=$((processed + 1))
        
        # 当达到批量大小或处理完所有key时，执行删除
        local batch_count
        batch_count=$(wc -l < "$batch_temp" | tr -d ' ')
        
        if [ "$batch_count" -eq "$BATCH_SIZE" ] || [ "$processed" -eq "$total_keys" ]; then
            # 构建删除命令参数
            local del_args
            del_args=$(tr '\n' ' ' < "$batch_temp")
            
            if $redis_cmd del $del_args >/dev/null 2>&1; then
                success_count=$((success_count + batch_count))
                log_info "✓ 成功删除 $batch_count 个key (进度: $processed/$total_keys)" >&2
            else
                fail_count=$((fail_count + batch_count))
                log_error "✗ 删除 $batch_count 个key失败" >&2
            fi
            
            # 清空批量临时文件
            > "$batch_temp"
        fi
    done < "$temp_file"
    
    log_info "删除完成: 成功 $success_count 个，失败 $fail_count 个" >&2
    
    # 清理临时文件
    rm -f "$temp_file" "$batch_temp"
}

# =============================================================================
# 主函数
# =============================================================================

# 解析命令行参数
parse_arguments() {
    while [ $# -gt 0 ]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -H|--host)
                REDIS_HOST="$2"
                shift 2
                ;;
            -P|--port)
                REDIS_PORT="$2"
                shift 2
                ;;
            -a|--auth)
                REDIS_PASSWORD="$2"
                shift 2
                ;;
            -n|--db)
                REDIS_DB="$2"
                shift 2
                ;;
            -b|--batch)
                BATCH_SIZE="$2"
                shift 2
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$SERVICE_IDS" ]; then
                    SERVICE_IDS="$1"
                else
                    log_error "只能指定一个service_id参数"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查必需参数
    if [ -z "$SERVICE_IDS" ]; then
        log_error "缺少必需参数: SERVICE_IDS"
        show_help
        exit 1
    fi
}

# 主函数
main() {
    # 解析参数
    parse_arguments "$@"
    
    log_info "=== Redis Key清理脚本 ==="
    log_info "Redis地址: $REDIS_HOST:$REDIS_PORT (DB: $REDIS_DB)"
    log_info "Service IDs: $SERVICE_IDS"
    log_info "批量大小: $BATCH_SIZE"
    
    if [ "$DRY_RUN" = true ]; then
        log_info "运行模式: 试运行（不会实际删除）"
    fi
    
    # 检查依赖
    check_dependencies
    
    # 测试连接
    if ! test_redis_connection; then
        exit 1
    fi
    
    # 获取匹配的key
    local keys_temp_file
    keys_temp_file=$(mktemp)
    
    if get_matching_keys 2>/dev/null > "$keys_temp_file"; then
        # 删除key
        if delete_keys < "$keys_temp_file"; then
            log_info "操作完成"
        else
            log_error "操作失败"
            rm -f "$keys_temp_file"
            exit 1
        fi
    else
        log_error "操作失败"
        rm -f "$keys_temp_file"
        exit 1
    fi
    
    rm -f "$keys_temp_file"
}

# 执行主函数
 main "$@"