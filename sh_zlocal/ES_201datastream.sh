#!/bin/bash

# 设置Elasticsearch的URL
ES_URL="http://192.168.31.201:9200"
ES_USER="elastic"
ES_PASSWD="rCa6gSeEizCjlfrwAj3R"

# 设置要查找的关键字
KEYWORD="2025.10"

# 查找包含关键字的数据流
DATA_STREAMS=$(curl -s -u "$ES_USER:$ES_PASSWD" -X GET "$ES_URL/_data_stream/*$KEYWORD*" | jq -r '.data_streams[].name')

# 遍历每个数据流
for DATA_STREAM in $DATA_STREAMS; do
    echo "\nProcessing data stream: $DATA_STREAM"

    # 获取数据流的索引列表
    INDEXES=$(curl -s -u "$ES_USER:$ES_PASSWD" -X GET "$ES_URL/_data_stream/$DATA_STREAM" | jq -r '.data_streams[0].indices[].index_name')

    # 删除数据流
    echo "Deleting data stream: $DATA_STREAM"
    curl -s -u "$ES_USER:$ES_PASSWD" -X DELETE "$ES_URL/_data_stream/$DATA_STREAM"

    # 删除每个索引
    for INDEX in $INDEXES; do
        echo "Deleting index: $INDEX"
        curl -s -u "$ES_USER:$ES_PASSWD" -X DELETE "$ES_URL/$INDEX"
    done
    
done
