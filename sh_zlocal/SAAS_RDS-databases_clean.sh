#!/bin/bash

# =============================================================================
# RDS数据库清理脚本
# 功能：根据指定的数据库前缀批量删除MySQL/RDS数据库
# 作者：自动化运维脚本
# 版本：1.0
# =============================================================================

# ===
# ssh u125r
# cd /user/sqldata
# ===

set -uo pipefail  # 严格模式：未定义变量和管道错误时退出，但允许命令失败继续执行

# =============================================================================
# 全局变量配置
# =============================================================================

# 数据库连接配置
DB_HOST="${DB_HOST:-**************}"          # 数据库主机地址
DB_PORT="${DB_PORT:-33068}"               # 数据库端口
DB_USER="${DB_USER:-root}"               # 数据库用户名
DB_PASSWORD=${DB_PASSWORD:-'J#fe9Yu{'}           # 数据库密码
DB_PREFIXES=""                           # 数据库前缀列表（逗号分隔）

# 脚本配置
SCRIPT_NAME=$(basename "$0")
LOG_LEVEL="INFO"                         # 日志级别
DRY_RUN=false                            # 是否为试运行模式

# 颜色配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# 日志函数
# =============================================================================

# 记录信息日志
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" >&2
}

# 记录警告日志
log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1" >&2
}

# 记录错误日志
log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

# 记录成功日志
log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" >&2
}

# =============================================================================
# 工具函数
# =============================================================================

# 显示帮助信息
show_help() {
    cat << EOF
RDS数据库清理脚本

使用方法:
  $SCRIPT_NAME [OPTIONS] DATABASE_PREFIXES

参数说明:
  DATABASE_PREFIXES  - 数据库前缀，支持多个前缀用英文逗号分隔

选项:
  -h, --help         显示此帮助信息
  -d, --dry-run      试运行模式，只显示将要删除的数据库，不实际删除
  -H, --host HOST    数据库主机地址 (默认: localhost)
  -P, --port PORT    数据库端口 (默认: 3306)
  -u, --user USER    数据库用户名 (默认: root)
  -p, --password PWD 数据库密码

环境变量:
  DB_HOST            数据库主机地址
  DB_PORT            数据库端口
  DB_USER            数据库用户名
  DB_PASSWORD        数据库密码

示例:
  $SCRIPT_NAME "test_,dev_,staging_"
  $SCRIPT_NAME -H ************* -u admin -p mypassword "app1_,app2_"
  $SCRIPT_NAME --dry-run "temp_,backup_"

EOF
}

# 检查依赖
check_dependencies() {
    local missing_deps=()
    
    # 检查mysql命令
    if ! command -v mysql >/dev/null 2>&1; then
        missing_deps+=("mysql")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "缺少必要的依赖: ${missing_deps[*]}"
        log_error "请安装MySQL客户端工具"
        exit 1
    fi
}

# =============================================================================
# 数据库操作函数
# =============================================================================

# 测试数据库连接
test_db_connection() {
    log_info "测试数据库连接: $DB_HOST:$DB_PORT"
    
    local mysql_cmd="mysql -h$DB_HOST -P$DB_PORT -u$DB_USER"
    if [ -n "$DB_PASSWORD" ]; then
        mysql_cmd="$mysql_cmd -p$DB_PASSWORD"
    fi
    
    if $mysql_cmd -e "SELECT 1" >/dev/null 2>&1; then
        log_success "数据库连接测试成功"
        return 0
    else
        log_error "无法连接到数据库服务器"
        log_error "请检查数据库地址、端口、用户名和密码是否正确"
        return 1
    fi
}

# 获取匹配前缀的数据库列表
get_matching_databases() {
    # 将逗号分隔的前缀字符串转换为数组
    IFS=',' read -ra PREFIX_ARRAY <<< "$DB_PREFIXES"
    
    log_info "查找前缀为 '$DB_PREFIXES' 的数据库..." >&2
    
    local mysql_cmd="mysql -h$DB_HOST -P$DB_PORT -u$DB_USER"
    if [ -n "$DB_PASSWORD" ]; then
        mysql_cmd="$mysql_cmd -p$DB_PASSWORD"
    fi
    
    # 获取所有数据库
    local databases
    databases=$($mysql_cmd -e "SHOW DATABASES;" -s -N 2>/dev/null | grep -v -E '^(information_schema|performance_schema|mysql|sys)$')
    
    # 过滤匹配前缀的数据库
    local matching_databases=()
    for database in $databases; do
        # 检查数据库是否匹配任何一个前缀
        for prefix in "${PREFIX_ARRAY[@]}"; do
            # 去除前缀两端的空格
            prefix=$(echo "$prefix" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
            if [[ $database == $prefix* ]]; then
                matching_databases+=("$database")
                break  # 找到匹配就跳出内层循环
            fi
        done
    done
    
    if [ ${#matching_databases[@]} -eq 0 ]; then
        log_warn "未找到前缀为 '$DB_PREFIXES' 的数据库" >&2
        return 1
    fi
    
    log_info "找到 ${#matching_databases[@]} 个匹配的数据库:" >&2
    for database in "${matching_databases[@]}"; do
        printf "  - %s\n" "$database" >&2
    done
    
    # 返回匹配的数据库数组（输出到stdout）
    printf "%s\n" "${matching_databases[@]}"
}

# 删除数据库
delete_databases() {
    local databases=("$@")
    local success_count=0
    local fail_count=0
    
    log_warn "即将删除 ${#databases[@]} 个数据库，此操作不可逆！" >&2
    
    if [ "$DRY_RUN" = true ]; then
        log_info "试运行模式：以下数据库将被删除（实际不会删除）:" >&2
        for database in "${databases[@]}"; do
            log_info "  - $database" >&2
        done
        return 0
    fi
    
    # 确认删除
    echo -n "确认删除以上数据库？(y/N): " >&2
    read -r confirmation
    if [[ ! $confirmation =~ ^[Yy]$ ]]; then
        log_info "操作已取消" >&2
        return 0
    fi
    
    local mysql_cmd="mysql -h$DB_HOST -P$DB_PORT -u$DB_USER"
    if [ -n "$DB_PASSWORD" ]; then
        mysql_cmd="$mysql_cmd -p$DB_PASSWORD"
    fi
    
    for database in "${databases[@]}"; do
        log_info "删除数据库: $database" >&2
        
        # 直接执行删除命令并捕获退出码，确保单个数据库删除失败不会影响其他数据库的删除
        $mysql_cmd -e "DROP DATABASE \`$database\`;" 2>/dev/null
        local exit_code=$?
        
        if [ $exit_code -eq 0 ]; then
            log_success "✓ 数据库 $database 删除成功" >&2
            ((success_count++))
        else
            log_error "✗ 数据库 $database 删除失败" >&2
            ((fail_count++))
        fi
    done
    
    log_info "删除完成: 成功 $success_count 个，失败 $fail_count 个" >&2
}

# =============================================================================
# 主函数
# =============================================================================

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -H|--host)
                DB_HOST="$2"
                shift 2
                ;;
            -P|--port)
                DB_PORT="$2"
                shift 2
                ;;
            -u|--user)
                DB_USER="$2"
                shift 2
                ;;
            -p|--password)
                DB_PASSWORD="$2"
                shift 2
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$DB_PREFIXES" ]; then
                    DB_PREFIXES="$1"
                else
                    log_error "只能指定一个数据库前缀参数"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查必需参数
    if [ -z "$DB_PREFIXES" ]; then
        log_error "缺少必需参数: DATABASE_PREFIXES"
        show_help
        exit 1
    fi
}

# 主函数
main() {
    # 解析参数
    parse_arguments "$@"
    
    log_info "=== RDS数据库清理脚本 ==="
    log_info "数据库地址: $DB_HOST:$DB_PORT"
    log_info "用户名: $DB_USER"
    log_info "数据库前缀: $DB_PREFIXES"
    
    if [ "$DRY_RUN" = true ]; then
        log_info "运行模式: 试运行（不会实际删除）"
    fi
    
    # 检查依赖
    check_dependencies
    
    # 测试连接
    if ! test_db_connection; then
        exit 1
    fi
    
    # 获取匹配的数据库
    local matching_databases_output
    matching_databases_output=$(get_matching_databases 2>/dev/null)
    
    if [ $? -ne 0 ] || [ -z "$matching_databases_output" ]; then
        exit 0
    fi
    
    local matching_databases=()
    if [ -n "$matching_databases_output" ]; then
        while IFS= read -r database; do
            [[ -n "$database" ]] && matching_databases+=("$database")
        done <<EOF
$matching_databases_output
EOF
    fi
    
    # 删除数据库
    if [ ${#matching_databases[@]} -gt 0 ]; then
        delete_databases "${matching_databases[@]}"
    fi
}

# 执行主函数
main "$@"
