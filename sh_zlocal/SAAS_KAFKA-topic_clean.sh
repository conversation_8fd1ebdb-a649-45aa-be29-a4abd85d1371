#!/bin/bash

# =============================================================================
# Kafka Topic清理脚本
# 功能：根据指定的Topic前缀批量删除Kafka中的Topic
# 作者：自动化运维脚本
# 版本：1.0
# =============================================================================

# ===
# ssh u125r
# cd /user/sqldata
# docker run --rm -it -w /opt/bitnami/kafka/bin -v /user/sqldata/SAAS_KAFKA-topic_clean.sh:/opt/bitnami/kafka/bin/SAAS_KAFKA-topic_clean.sh registry.yitaiyitai.com/bitnami/kafka:2.8.1 bash
#   sh ./SAAS_KAFKA-topic_clean.sh  -b **************:9092 -d "cbdtest"
# ===

set -eu  # 严格模式：遇到错误立即退出
# 注意：某些shell不支持pipefail选项，为了兼容性这里不使用

# =============================================================================
# 全局变量配置
# =============================================================================

# Kafka连接配置
KAFKA_BOOTSTRAP_SERVERS="${KAFKA_BOOTSTRAP_SERVERS:-localhost:9092}"  # Kafka服务器地址（容器内默认本地连接）
KAFKA_CONFIG_FILE="${KAFKA_CONFIG_FILE:-}"                                # Kafka配置文件路径（可选）
TOPIC_PREFIXES=""                                                         # Topic前缀列表（逗号分隔）

# 脚本配置
SCRIPT_NAME=$(basename "$0")
LOG_LEVEL="INFO"                         # 日志级别
DRY_RUN=false                            # 是否为试运行模式

# 颜色配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# 日志函数
# =============================================================================

# 记录信息日志
log_info() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1" >&2
}

# 记录警告日志
log_warn() {
    printf "${YELLOW}[WARN]${NC} %s\n" "$1" >&2
}

# 记录错误日志
log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1" >&2
}

# 记录成功日志
log_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1" >&2
}

# =============================================================================
# 工具函数
# =============================================================================

# 显示帮助信息
show_help() {
    cat << EOF
Kafka Topic清理脚本

使用方法:
  $SCRIPT_NAME [OPTIONS] TOPIC_PREFIXES

参数说明:
  TOPIC_PREFIXES     - Topic前缀，支持多个前缀用英文逗号分隔

选项:
  -h, --help         显示此帮助信息
  -d, --dry-run      试运行模式，只显示将要删除的Topic，不实际删除
  -b, --bootstrap SERVERS  Kafka Bootstrap服务器地址 (默认: localhost:9092，容器内推荐)
  -c, --config FILE  Kafka配置文件路径

环境变量:
  KAFKA_BOOTSTRAP_SERVERS  Kafka Bootstrap服务器地址
  KAFKA_CONFIG_FILE        Kafka配置文件路径

示例:
  $SCRIPT_NAME "test-,dev-,staging-"
  $SCRIPT_NAME -b *************:9092 "app1-,app2-"
  $SCRIPT_NAME --dry-run "temp-,backup-"
  $SCRIPT_NAME -c /path/to/kafka.properties "old-topic-"

EOF
}

# 检查依赖
check_dependencies() {
    # 检查kafka-topics命令（支持多种路径）
    if ! command -v kafka-topics >/dev/null 2>&1 && \
       ! command -v kafka-topics.sh >/dev/null 2>&1 && \
       ! [ -x "/opt/bitnami/kafka/bin/kafka-topics.sh" ]; then
        log_error "缺少必要的依赖: kafka-topics管理脚本"
        log_error "请确保Kafka管理脚本可用（检查路径: /opt/bitnami/kafka/bin/）"
        exit 1
    fi
}

# =============================================================================
# Kafka操作函数
# =============================================================================

# 构建Kafka命令
build_kafka_cmd() {
    # 按优先级查找kafka-topics命令
    local kafka_topics_cmd="kafka-topics"
    
    if [ -x "/opt/bitnami/kafka/bin/kafka-topics.sh" ]; then
        # Bitnami容器内的标准路径
        kafka_topics_cmd="/opt/bitnami/kafka/bin/kafka-topics.sh"
    elif command -v kafka-topics.sh >/dev/null 2>&1; then
        # PATH中的.sh版本
        kafka_topics_cmd="kafka-topics.sh"
    elif command -v kafka-topics >/dev/null 2>&1; then
        # PATH中的标准版本
        kafka_topics_cmd="kafka-topics"
    fi
    
    local kafka_cmd="$kafka_topics_cmd --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS"
    
    if [ -n "$KAFKA_CONFIG_FILE" ]; then
        kafka_cmd="$kafka_cmd --command-config $KAFKA_CONFIG_FILE"
    fi
    
    echo "$kafka_cmd"
}

# 测试Kafka连接
test_kafka_connection() {
    log_info "测试Kafka连接: $KAFKA_BOOTSTRAP_SERVERS"
    
    local kafka_cmd
    kafka_cmd=$(build_kafka_cmd)
    
    if $kafka_cmd --list >/dev/null 2>&1; then
        log_success "Kafka连接测试成功"
        return 0
    else
        log_error "无法连接到Kafka服务器"
        log_error "请检查Kafka服务器地址和配置是否正确"
        return 1
    fi
}

# 获取匹配前缀的Topic列表
get_matching_topics() {
    log_info "查找前缀为 '$TOPIC_PREFIXES' 的Topic..." >&2
    
    local kafka_cmd
    kafka_cmd=$(build_kafka_cmd)
    
    # 获取所有Topic
    local topics
    topics=$($kafka_cmd --list 2>/dev/null)
    
    # 创建临时文件存储匹配的Topic
    local temp_file
    temp_file=$(mktemp)
    local topic_count=0
    
    # 处理逗号分隔的TOPIC_PREFIXES
    # 使用here document避免子shell问题
    while IFS= read -r prefix; do
        # 去除前缀两端的空格
        prefix=$(echo "$prefix" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
        
        if [ -n "$prefix" ]; then
            # 检查每个Topic是否匹配当前前缀
            for topic in $topics; do
                # 使用case语句进行模式匹配，兼容低版本shell
                case "$topic" in
                    $prefix*)
                        # 检查是否已经添加过这个topic（避免重复）
                        if ! grep -q "^$topic$" "$temp_file" 2>/dev/null; then
                            echo "$topic" >> "$temp_file"
                        fi
                        ;;
                esac
            done
        fi
    done << EOF
$(echo "$TOPIC_PREFIXES" | tr ',' '\n')
EOF
    
    # 检查是否找到匹配的Topic
    topic_count=$(wc -l < "$temp_file" | tr -d ' ')
    
    if [ "$topic_count" -eq 0 ]; then
        log_warn "未找到前缀为 '$TOPIC_PREFIXES' 的Topic" >&2
        rm -f "$temp_file"
        return 1
    fi
    
    log_info "找到 $topic_count 个匹配的Topic:" >&2
    while IFS= read -r topic; do
        printf "  - %s\n" "$topic" >&2
    done < "$temp_file"
    
    # 输出匹配的Topic并清理临时文件
    cat "$temp_file"
    rm -f "$temp_file"
}

# 删除Topic
delete_topics() {
    # 从标准输入读取Topic列表
    local temp_file
    temp_file=$(mktemp)
    cat > "$temp_file"
    
    local total_topics
    total_topics=$(wc -l < "$temp_file" | tr -d ' ')
    
    if [ "$total_topics" -eq 0 ]; then
        rm -f "$temp_file"
        return 0
    fi
    
    log_warn "即将删除 $total_topics 个Topic，此操作不可逆！" >&2
    
    if [ "$DRY_RUN" = true ]; then
        log_info "试运行模式：以下Topic将被删除（实际不会删除）:" >&2
        while IFS= read -r topic; do
            log_info "  - $topic" >&2
        done < "$temp_file"
        rm -f "$temp_file"
        return 0
    fi
    
    # 确认删除
    printf "确认删除以上Topic？(y/N): " >&2
    read -r confirmation < /dev/tty
    if [ "$confirmation" != "y" ] && [ "$confirmation" != "Y" ]; then
        log_info "操作已取消" >&2
        rm -f "$temp_file"
        return 0
    fi
    
    local kafka_cmd
    kafka_cmd=$(build_kafka_cmd)
    
    local success_count=0
    local fail_count=0
    
    while IFS= read -r topic; do
        log_info "删除Topic: $topic" >&2
        
        if $kafka_cmd --delete --topic "$topic" 2>/dev/null; then
            log_success "✓ Topic $topic 删除成功" >&2
            success_count=$((success_count + 1))
        else
            log_error "✗ Topic $topic 删除失败" >&2
            fail_count=$((fail_count + 1))
        fi
    done < "$temp_file"
    
    log_info "删除完成: 成功 $success_count 个，失败 $fail_count 个" >&2
    
    # 清理临时文件
    rm -f "$temp_file"
}

# =============================================================================
# 主函数
# =============================================================================

# 解析命令行参数
parse_arguments() {
    while [ $# -gt 0 ]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -b|--bootstrap)
                KAFKA_BOOTSTRAP_SERVERS="$2"
                shift 2
                ;;
            -c|--config)
                KAFKA_CONFIG_FILE="$2"
                shift 2
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$TOPIC_PREFIXES" ]; then
                    TOPIC_PREFIXES="$1"
                else
                    log_error "只能指定一个Topic前缀参数"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查必需参数
    if [ -z "$TOPIC_PREFIXES" ]; then
        log_error "缺少必需参数: TOPIC_PREFIXES"
        show_help
        exit 1
    fi
}

# 主函数
main() {
    # 解析参数
    parse_arguments "$@"
    
    log_info "=== Kafka Topic清理脚本 ==="
    log_info "Kafka服务器: $KAFKA_BOOTSTRAP_SERVERS"
    log_info "Topic前缀: $TOPIC_PREFIXES"
    
    if [ -n "$KAFKA_CONFIG_FILE" ]; then
        log_info "配置文件: $KAFKA_CONFIG_FILE"
    fi
    
    if [ "$DRY_RUN" = true ]; then
        log_info "运行模式: 试运行（不会实际删除）"
    fi
    
    # 检查依赖
    check_dependencies
    
    # 测试连接
    if ! test_kafka_connection; then
        exit 1
    fi
    
    # 获取匹配的Topic
    local topics_temp_file
    topics_temp_file=$(mktemp)
    
    if get_matching_topics 2>/dev/null > "$topics_temp_file"; then
        # 删除Topic
        if delete_topics < "$topics_temp_file"; then
            log_info "操作完成"
        else
            log_error "操作失败"
            rm -f "$topics_temp_file"
            exit 1
        fi
    else
        log_error "操作失败"
        rm -f "$topics_temp_file"
        exit 1
    fi
    
    rm -f "$topics_temp_file"
}

# 执行主函数
main "$@"