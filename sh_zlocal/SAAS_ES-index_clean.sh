#!/bin/bash

# ES索引清理脚本
# 功能：清理指定ES中带有指定前缀的索引
# 使用方法：./SAAS_ES-index_clean.sh [ES_URL] [USERNAME] [PASSWORD] [INDEX_PREFIXES]
# ./SAAS_ES-index_clean.sh -h

# 默认配置
DEFAULT_ES_URL="http://192.168.31.195:9200"
DEFAULT_ES_USER=""
DEFAULT_ES_PASSWD=""
DEFAULT_INDEX_PREFIXES="logstash-"

# 全局变量
DRY_RUN=false

# 解析命令行参数
parse_arguments() {
    local options_done=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--dry-run)
                if [ "$options_done" = true ]; then
                    # 选项解析完成后，当作位置参数处理
                    if [ -z "$ES_URL_ARG" ]; then
                        ES_URL_ARG="$1"
                    elif [ -z "$ES_USER_ARG" ]; then
                        ES_USER_ARG="$1"
                    elif [ -z "$ES_PASSWD_ARG" ]; then
                        ES_PASSWD_ARG="$1"
                    elif [ -z "$INDEX_PREFIXES_ARG" ]; then
                        INDEX_PREFIXES_ARG="$1"
                    else
                        log_error "过多的参数: $1"
                        show_help
                        exit 1
                    fi
                else
                    DRY_RUN=true
                fi
                shift
                ;;
            -h|--help)
                if [ "$options_done" = true ]; then
                    # 选项解析完成后，当作位置参数处理
                    if [ -z "$ES_URL_ARG" ]; then
                        ES_URL_ARG="$1"
                    elif [ -z "$ES_USER_ARG" ]; then
                        ES_USER_ARG="$1"
                    elif [ -z "$ES_PASSWD_ARG" ]; then
                        ES_PASSWD_ARG="$1"
                    elif [ -z "$INDEX_PREFIXES_ARG" ]; then
                        INDEX_PREFIXES_ARG="$1"
                    else
                        log_error "过多的参数: $1"
                        show_help
                        exit 1
                    fi
                    shift
                else
                    show_help
                    exit 0
                fi
                ;;
            -*)
                if [ "$options_done" = true ] || [ "$1" = "-" ]; then
                    # 选项解析完成后，或者是单独的"-"，当作位置参数处理
                    if [ -z "$ES_URL_ARG" ]; then
                        ES_URL_ARG="$1"
                    elif [ -z "$ES_USER_ARG" ]; then
                        ES_USER_ARG="$1"
                    elif [ -z "$ES_PASSWD_ARG" ]; then
                        ES_PASSWD_ARG="$1"
                    elif [ -z "$INDEX_PREFIXES_ARG" ]; then
                        INDEX_PREFIXES_ARG="$1"
                    else
                        log_error "过多的参数: $1"
                        show_help
                        exit 1
                    fi
                    shift
                else
                    log_error "未知选项: $1"
                    show_help
                    exit 1
                fi
                ;;
            *)
                # 位置参数开始，标记选项解析完成
                options_done=true
                if [ -z "$ES_URL_ARG" ]; then
                    ES_URL_ARG="$1"
                elif [ -z "$ES_USER_ARG" ]; then
                    ES_USER_ARG="$1"
                elif [ -z "$ES_PASSWD_ARG" ]; then
                    ES_PASSWD_ARG="$1"
                elif [ -z "$INDEX_PREFIXES_ARG" ]; then
                    INDEX_PREFIXES_ARG="$1"
                else
                    log_error "过多的参数: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 设置最终参数值，处理特殊标记
    ES_URL=${ES_URL_ARG:-$DEFAULT_ES_URL}
    
    # 处理用户名：如果是 "-" 或 "NONE"，则设为空
    if [ "$ES_USER_ARG" = "-" ] || [ "$ES_USER_ARG" = "NONE" ]; then
        ES_USER=""
    else
        ES_USER=${ES_USER_ARG:-$DEFAULT_ES_USER}
    fi
    
    # 处理密码：如果是 "-" 或 "NONE"，则设为空
    if [ "$ES_PASSWD_ARG" = "-" ] || [ "$ES_PASSWD_ARG" = "NONE" ]; then
        ES_PASSWD=""
    else
        ES_PASSWD=${ES_PASSWD_ARG:-$DEFAULT_ES_PASSWD}
    fi
    
    INDEX_PREFIXES=${INDEX_PREFIXES_ARG:-$DEFAULT_INDEX_PREFIXES}
}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    printf "${GREEN}[INFO]${NC} %s\n" "$1"
}

log_warn() {
    printf "${YELLOW}[WARN]${NC} %s\n" "$1"
}

log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

# 检查必要工具
check_dependencies() {
    if ! command -v curl &> /dev/null; then
        log_error "curl 命令未找到，请先安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq 命令未找到，请先安装 jq"
        exit 1
    fi
}

# 构建curl认证参数
build_curl_auth() {
    if [ -n "$ES_USER" ] && [ -n "$ES_PASSWD" ]; then
        echo "-u $ES_USER:$ES_PASSWD"
    else
        echo ""
    fi
}

# 测试ES连接
test_es_connection() {
    log_info "测试ES连接: $ES_URL"
    
    auth_param=$(build_curl_auth)
    response=$(curl -s -w "%{http_code}" $auth_param -X GET "$ES_URL/_cluster/health" -o /dev/null)
    
    if [ "$response" != "200" ]; then
        log_error "无法连接到ES集群，HTTP状态码: $response"
        if [ -n "$ES_USER" ] || [ -n "$ES_PASSWD" ]; then
            log_error "请检查ES地址、用户名和密码是否正确"
        else
            log_error "请检查ES地址是否正确，或ES是否需要认证"
        fi
        exit 1
    fi
    
    log_info "ES连接测试成功"
}

# 获取匹配前缀的索引列表
get_matching_indexes() {
    # 将逗号分隔的前缀字符串转换为数组
    IFS=',' read -ra PREFIX_ARRAY <<< "$INDEX_PREFIXES"
    
    log_info "查找前缀为 '$INDEX_PREFIXES' 的索引..." >&2
    
    # 获取所有索引
    auth_param=$(build_curl_auth)
    indexes=$(curl -s $auth_param -X GET "$ES_URL/_cat/indices?format=json" | jq -r '.[].index')
    
    # 过滤匹配前缀的索引
    matching_indexes=()
    for index in $indexes; do
        # 检查索引是否匹配任何一个前缀
        for prefix in "${PREFIX_ARRAY[@]}"; do
            # 去除前缀两端的空格
            prefix=$(echo "$prefix" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
            if [[ $index == $prefix* ]]; then
                matching_indexes+=("$index")
                break  # 找到匹配就跳出内层循环
            fi
        done
    done
    
    if [ ${#matching_indexes[@]} -eq 0 ]; then
        log_warn "未找到前缀为 '$INDEX_PREFIXES' 的索引" >&2
        exit 0
    fi
    
    log_info "找到 ${#matching_indexes[@]} 个匹配的索引:" >&2
    for index in "${matching_indexes[@]}"; do
        printf "  - %s\n" "$index" >&2  # 输出到stderr避免干扰返回值
    done
    
    # 返回匹配的索引数组（输出到stdout）
    printf "%s\n" "${matching_indexes[@]}"
}

# 删除索引
delete_indexes() {
    local indexes=("$@")
    
    if [ "$DRY_RUN" = true ]; then
        log_warn "[DRY-RUN 模式] 将要删除以下 ${#indexes[@]} 个索引（实际不会删除）："
        for index in "${indexes[@]}"; do
            printf "  ${YELLOW}[DRY-RUN]${NC} 索引: %s\n" "$index"
        done
        log_info "[DRY-RUN 模式] 预览完成，如需实际删除请移除 -d 或 --dry-run 参数"
        return 0
    fi
    
    log_warn "即将删除 ${#indexes[@]} 个索引，此操作不可逆！"
    
    success_count=0
    fail_count=0
    
    for index in "${indexes[@]}"; do
        log_info "删除索引: $index"
        
        auth_param=$(build_curl_auth)
        response=$(curl -s -w "%{http_code}" $auth_param -X DELETE "$ES_URL/$index" -o /dev/null)
        
        if [ "$response" = "200" ]; then
            log_info "✓ 索引 $index 删除成功"
            ((success_count++))
        else
            log_error "✗ 索引 $index 删除失败，HTTP状态码: $response"
            ((fail_count++))
        fi
    done
    
    log_info "删除完成！成功: $success_count, 失败: $fail_count"
}

# 显示帮助信息
show_help() {
    echo "ES索引清理脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项] [ES_URL] [USERNAME] [PASSWORD] [INDEX_PREFIXES]"
    echo ""
    echo "选项:"
    echo "  -d, --dry-run  - 试运行模式，只显示要删除的索引而不实际删除"
    echo "  -h, --help     - 显示此帮助信息"
    echo ""
    echo "参数说明:"
    echo "  ES_URL         - Elasticsearch地址 (默认: $DEFAULT_ES_URL)"
    echo "  USERNAME       - 用户名 (默认: $DEFAULT_ES_USER)"
    echo "  PASSWORD       - 密码 (默认: $DEFAULT_ES_PASSWD)"
    echo "  INDEX_PREFIXES - 索引前缀，支持多个前缀用英文逗号分隔 (默认: $DEFAULT_INDEX_PREFIXES)"
    echo ""
    echo "示例:"
    echo "  # 试运行模式，预览要删除的索引"
    echo "  $0 -d http://192.168.1.100:9200 elastic mypassword logstash-2024"
    echo "  $0 --dry-run http://192.168.1.100:9200 elastic mypassword \"logstash-,filebeat-\""
    echo ""
    echo "  # 无认证ES（使用 \"-\" 或 \"NONE\" 表示空的用户名密码）"
    echo "  $0 -d http://192.168.1.100:9200 - - logstash-2024"
    echo "  $0 http://192.168.1.100:9200 NONE NONE \"logstash-,filebeat-\""
    echo ""
    echo "  # 实际删除索引"
    echo "  $0 http://192.168.1.100:9200 elastic mypassword logstash-2024"
    echo "  $0 http://192.168.1.100:9200 elastic mypassword \"logstash-,filebeat-,metricbeat-\""
    echo ""
    echo "  # 使用默认配置"
    echo "  $0 -d  # 试运行模式"
    echo "  $0     # 实际删除"
    echo ""
}

# 主函数
main() {
    # 解析命令行参数
    parse_arguments "$@"
    
    log_info "=== ES索引清理脚本 ==="
    log_info "ES地址: $ES_URL"
    log_info "用户名: $ES_USER"
    log_info "索引前缀: $INDEX_PREFIXES"
    if [ "$DRY_RUN" = true ]; then
        log_info "运行模式: DRY-RUN (试运行)"
    else
        log_info "运行模式: 实际删除"
    fi
    echo ""
    
    # 检查依赖
    check_dependencies
    
    # 测试连接
    test_es_connection
    
    # 获取匹配的索引
    matching_indexes_output=$(get_matching_indexes 2>/dev/null)
    matching_indexes=()
    while IFS= read -r index; do
        [[ -n "$index" ]] && matching_indexes+=("$index")
    done <<< "$matching_indexes_output"
    
    # 删除索引
    if [ ${#matching_indexes[@]} -gt 0 ]; then
        delete_indexes "${matching_indexes[@]}"
    fi
}

# 执行主函数
main "$@"