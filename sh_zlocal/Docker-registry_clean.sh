#!/bin/bash

# 删除 Docker Registry 中指定镜像的所有版本
# 使用方法: ./Docker-registry_clean.sh [OPTIONS] <registry_url> <repository> [username] [password]
# 例如: ./Docker-registry_clean.sh registry.yitaiyitai.com bpmax/bpmax-prod
# 如果 Registry 需要认证，可以提供用户名和密码: ./Docker-registry_clean.sh registry.yitaiyitai.com bpmax/bpmax-prod username password
# 选项:
#   --dry-run, -d    预览模式，只显示要删除的标签，不实际删除
#   --https          使用 HTTPS 协议（默认自动检测）
#   --help, -h       显示帮助信息

set -e

# 默认参数
DRY_RUN=false
PROTOCOL=""
REGISTRY_URL=""
REPOSITORY=""
USERNAME=""
PASSWORD=""

# 显示帮助信息
show_help() {
    cat << EOF
Docker Registry 清理脚本

用法: $0 [OPTIONS] <registry_url> <repository> [username] [password]

参数:
  registry_url     Registry 服务器地址
  repository       镜像仓库名称
  username         可选的认证用户名
  password         可选的认证密码

选项:
  --dry-run, -d    预览模式，只显示要删除的标签，不实际删除
  --https          强制使用 HTTPS 协议
  --help, -h       显示此帮助信息

示例:
  $0 registry.example.com myapp/backend
  $0 --dry-run registry.example.com myapp/backend
  $0 --https registry.example.com myapp/backend username password

EOF
}

# 解析命令行参数
while [ $# -gt 0 ]; do
    case $1 in
        --dry-run|-d)
            DRY_RUN=true
            shift
            ;;
        --https)
            PROTOCOL="https"
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        -*)
            echo "错误: 未知选项 $1"
            show_help
            exit 1
            ;;
        *)
            if [ -z "$REGISTRY_URL" ]; then
                REGISTRY_URL=$1
            elif [ -z "$REPOSITORY" ]; then
                REPOSITORY=$1
            elif [ -z "$USERNAME" ]; then
                USERNAME=$1
            elif [ -z "$PASSWORD" ]; then
                PASSWORD=$1
            else
                echo "错误: 参数过多"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

if [ -z "$REGISTRY_URL" ] || [ -z "$REPOSITORY" ]; then
  echo "错误: 缺少必需参数"
  show_help
  exit 1
fi

# 检查依赖
check_dependencies() {
    local missing_deps=""
    
    # 检查 curl
    if ! command -v curl >/dev/null 2>&1; then
        missing_deps="${missing_deps}curl "
    fi
    
    # 检查 jq
    if ! command -v jq >/dev/null 2>&1; then
        missing_deps="${missing_deps}jq "
    fi
    
    if [ -n "$missing_deps" ]; then
        echo "错误: 缺少必需的依赖命令: $missing_deps"
        echo "请安装缺少的依赖后重试"
        echo ""
        echo "安装方法:"
        echo "  macOS: brew install curl jq"
        echo "  Ubuntu/Debian: apt-get install curl jq"
        echo "  CentOS/RHEL: yum install curl jq"
        exit 1
    fi
}

# 自动检测协议
detect_protocol() {
    if [ -n "$PROTOCOL" ]; then
        echo "$PROTOCOL"
        return
    fi
    
    # 尝试 HTTPS
    if curl -s --connect-timeout 5 "https://${REGISTRY_URL}/v2/" >/dev/null 2>&1; then
        echo "https"
    else
        echo "http"
    fi
}

# 执行依赖检查
check_dependencies

# 检测协议
PROTOCOL=$(detect_protocol)
echo "使用协议: $PROTOCOL"

# 构建认证头部（如果提供了认证信息）
AUTH_HEADER=""
if [ -n "$USERNAME" ] && [ -n "$PASSWORD" ]; then
  echo "使用提供的认证信息"
  AUTH_HEADER="-u ${USERNAME}:${PASSWORD}"
fi

# 获取所有标签
echo "获取镜像 ${REPOSITORY} 的所有标签..."
TAGS_RESPONSE=$(curl -s ${AUTH_HEADER} "${PROTOCOL}://${REGISTRY_URL}/v2/${REPOSITORY}/tags/list")
TAGS=$(echo $TAGS_RESPONSE | jq -r '.tags[]' 2>/dev/null)

if [ $? -ne 0 ] || [ -z "$TAGS" ]; then
  echo "没有找到标签或获取标签失败"
  echo "API 响应: $TAGS_RESPONSE"
  exit 1
fi

# 如果是 dry-run 模式，只显示标签列表
if [ "$DRY_RUN" = true ]; then
    echo ""
    echo "=== DRY-RUN 模式 ==="
    echo "以下标签将被删除:"
    for TAG in $TAGS; do
        echo "  - $TAG"
    done
    echo ""
    echo "共 $(echo $TAGS | wc -w) 个标签"
    echo "要实际执行删除，请移除 --dry-run 参数"
    exit 0
fi

# 遍历并删除每个标签
for TAG in $TAGS; do
  echo "正在处理标签: ${TAG}"
  
  # 获取清单摘要
  DIGEST=$(curl -s ${AUTH_HEADER} \
    -H "Accept: application/vnd.docker.distribution.manifest.v2+json" \
    -I "${PROTOCOL}://${REGISTRY_URL}/v2/${REPOSITORY}/manifests/${TAG}" | grep Docker-Content-Digest | awk '{print $2}' | tr -d '\r')
  
  if [ -z "$DIGEST" ]; then
    echo "无法获取标签 ${TAG} 的摘要，尝试获取 schema v1 清单..."
    DIGEST=$(curl -s ${AUTH_HEADER} \
      -H "Accept: application/vnd.docker.distribution.manifest.v1+json" \
      -I "${PROTOCOL}://${REGISTRY_URL}/v2/${REPOSITORY}/manifests/${TAG}" | grep Docker-Content-Digest | awk '{print $2}' | tr -d '\r')
    
    if [ -z "$DIGEST" ]; then
      echo "无法获取标签 ${TAG} 的摘要，跳过"
      continue
    fi
  fi
  
  echo "删除标签 ${TAG}，摘要: ${DIGEST}"
  
  # 删除清单
  HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" \
    ${AUTH_HEADER} \
    -X DELETE "${PROTOCOL}://${REGISTRY_URL}/v2/${REPOSITORY}/manifests/${DIGEST}")
  
  if [ "$HTTP_CODE" = "202" ]; then
    echo "标签 ${TAG} 删除成功"
  else
    echo "标签 ${TAG} 删除失败，HTTP 状态码: ${HTTP_CODE}"
  fi
done

echo "所有标签处理完成。请注意，您可能需要在 Registry 上运行垃圾回收以释放磁盘空间。"
echo "垃圾回收命令: docker exec -it <registry_container> registry garbage-collect /etc/docker/registry/config.yml"
