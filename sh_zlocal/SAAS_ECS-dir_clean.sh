#!/bin/bash

# =============================================================================
# ECS目录查询脚本
# 功能：根据指定的service_id前缀查询对应的文件夹
# 作者：自动化运维脚本
# 版本：1.0
# =============================================================================

set -eu  # 严格模式：遇到错误立即退出
# 注意：某些shell不支持pipefail选项，为了兼容性这里不使用

# =============================================================================
# 全局变量配置
# =============================================================================

# 查询配置
ROOT_DIR="/user/data/saas/"                    # 固定的根目录
SERVICE_IDS=""                                  # service_id前缀列表（逗号分隔）

# 脚本配置
SCRIPT_NAME=$(basename "$0")
LOG_LEVEL="INFO"                         # 日志级别

# 颜色配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# 日志函数
# =============================================================================

# 记录信息日志
log_info() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1" >&2
}

# 记录警告日志
log_warn() {
    printf "${YELLOW}[WARN]${NC} %s\n" "$1" >&2
}

# 记录错误日志
log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1" >&2
}

# 记录成功日志
log_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1" >&2
}

# =============================================================================
# 工具函数
# =============================================================================

# 显示帮助信息
show_help() {
    cat << EOF
ECS目录查询脚本

使用方法:
  $SCRIPT_NAME [OPTIONS] SERVICE_IDS

参数说明:
  SERVICE_IDS        - service_id前缀，支持多个前缀用英文逗号分隔

选项:
  -h, --help         显示此帮助信息
  -r, --root DIR     指定根目录 (默认: $ROOT_DIR)

环境变量:
  ECS_ROOT_DIR       ECS根目录路径

示例:
  $SCRIPT_NAME "svc014r343z2v1p6m7t7a32576y4j,svc014r341t216p1h4x1s2v6c0r4q"
  $SCRIPT_NAME -r /custom/path "app1,app2"
  $SCRIPT_NAME "test-service"

EOF
}

# 检查依赖
check_dependencies() {
    # 检查基本命令
    if ! command -v find >/dev/null 2>&1; then
        log_error "缺少必要的依赖: find命令"
        exit 1
    fi
}

# =============================================================================
# 目录查询函数
# =============================================================================

# 检查根目录是否存在
check_root_directory() {
    if [ ! -d "$ROOT_DIR" ]; then
        log_error "根目录不存在: $ROOT_DIR"
        return 1
    fi
    
    if [ ! -r "$ROOT_DIR" ]; then
        log_error "根目录无读取权限: $ROOT_DIR"
        return 1
    fi
    
    log_info "根目录检查通过: $ROOT_DIR"
    return 0
}

# 查询匹配前缀的目录
query_matching_directories() {
    log_info "在根目录 '$ROOT_DIR' 中查找前缀为 '$SERVICE_IDS' 的目录..." >&2
    
    # 创建临时文件存储匹配的目录
    local temp_file
    temp_file=$(mktemp)
    local found_count=0
    
    # 处理逗号分隔的前缀
    local prefix
    echo "$SERVICE_IDS" | tr ',' '\n' | while IFS= read -r prefix; do
        # 跳过空前缀
        if [ -z "$prefix" ]; then
            continue
        fi
        
        # 查找匹配的目录
        find "$ROOT_DIR" -maxdepth 3 -type d -name "${prefix}*" 2>/dev/null | while IFS= read -r dir; do
            if [ -d "$dir" ]; then
                echo "$dir" >> "$temp_file"
                found_count=$((found_count + 1))
            fi
        done
    done
    
    # 读取并输出结果
    if [ -s "$temp_file" ]; then
        log_success "找到匹配的目录:" >&2
        
        # 收集所有匹配的目录路径
        local matched_dirs=""
        while IFS= read -r dir; do
            log_info "  - $dir" >&2
            if [ -z "$matched_dirs" ]; then
                matched_dirs="$dir"
            else
                matched_dirs="$matched_dirs $dir"
            fi
        done < "$temp_file"
        
        # 统计数量
        local total_count
        total_count=$(wc -l < "$temp_file")
        log_info "共找到 $total_count 个匹配的目录" >&2
        
        # 最后一次性打印所有匹配的目录，使用空格分隔
        echo "$matched_dirs"
    else
        log_warn "未找到匹配前缀 '$SERVICE_IDS' 的目录" >&2
    fi
    
    # 清理临时文件
    rm -f "$temp_file"
}

# =============================================================================
# 主函数
# =============================================================================

# 解析命令行参数
parse_arguments() {
    while [ $# -gt 0 ]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -r|--root)
                ROOT_DIR="$2"
                shift 2
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$SERVICE_IDS" ]; then
                    SERVICE_IDS="$1"
                else
                    log_error "只能指定一个service_id参数"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查必需参数
    if [ -z "$SERVICE_IDS" ]; then
        log_error "缺少必需参数: SERVICE_IDS"
        show_help
        exit 1
    fi
}

# 主函数
main() {
    # 检查环境变量
    if [ -n "${ECS_ROOT_DIR:-}" ]; then
        ROOT_DIR="$ECS_ROOT_DIR"
    fi
    
    # 解析参数
    parse_arguments "$@"
    
    log_info "=== ECS目录查询脚本 ==="
    log_info "根目录: $ROOT_DIR"
    log_info "Service IDs: $SERVICE_IDS"
    
    # 检查依赖
    check_dependencies
    
    # 检查根目录
    if ! check_root_directory; then
        exit 1
    fi
    
    # 查询匹配的目录
    query_matching_directories
    
    log_info "查询完成"
}

# 执行主函数
main "$@"