#!/bin/bash

# MySQL测试链接
echo -e "\e[34m设置MySQL连接信息...\e[0m"
MYSQL_HOST=**************
MYSQL_PORT=33060
MYSQL_USER=root
MYSQL_PASSWORD=J#fe9Yu{
MYSQL_DATABASE=dry-run
MYSQL_TABLES="migrations think_category think_configuration think_data_set think_data_set_data_set_fields_think_data_set_field think_data_set_field think_flow think_flow_page_template think_form_rule think_form_template think_group_model think_hook think_organization think_page_template think_platform think_plugin think_role think_user_model think_user_role think_workspace think_workspace_flow"

# 输出文件
echo -e "\e[34m设置输出文件信息...\e[0m"
OUTPUT_DIR=/Users/<USER>/Work/zero-code/ansible/bash
DATE=$(date +%Y-%m-%d_%H-%M-%S)
OUTPUT_FILE=$OUTPUT_DIR/mysql_dump_systemdata_$DATE.sql

# 运行mysqldump命令将数据导出到文件
echo -e "\e[34m正在导出数据到文件...\e[0m"
docker run --rm -it \
  -v $OUTPUT_DIR:/data \
  -e DATE=$DATE \
  -e MYSQL_HOST=$MYSQL_HOST \
  -e MYSQL_PORT=$MYSQL_PORT \
  -e MYSQL_USER=$MYSQL_USER \
  -e MYSQL_PASSWORD=$MYSQL_PASSWORD \
  -e MYSQL_DATABASE=$MYSQL_DATABASE \
  -e MYSQL_TABLES="$MYSQL_TABLES" \
  mysql:5.7.42 \
  sh -c 'exec mysqldump --host="$MYSQL_HOST" --port="$MYSQL_PORT" --user="$MYSQL_USER" --password="$MYSQL_PASSWORD" --routines "$MYSQL_DATABASE" $MYSQL_TABLES > /data/mysql_dump_systemdata_$DATE.sql'

# 打印成功信息
echo -e "\e[32m数据成功导出到文件 $OUTPUT_FILE\e[0m"

# 压缩输出文件
echo -e "\e[34m正在压缩输出文件...\e[0m"
cd $OUTPUT_DIR
tar -zcf mysql_dump_systemdata_$DATE.tar.gz mysql_dump_systemdata_$DATE.sql

rm $OUTPUT_FILE

# 仅保留最新的10个备份
echo -e "\e[34m仅保留最新的10个备份...\e[0m"
ls -t mysql_dump_systemdata_*.tar.gz | tail -n +11 | xargs rm --
