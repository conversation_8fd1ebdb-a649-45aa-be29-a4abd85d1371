#!/bin/bash

tar -zxf release.tar.gz

cd dist

cat .env

mkdir files

tar -zxf files.tar.gz -C files

cd files

rm -f Dockerfile

find . -regex ".*\.map" -print0 | xargs -0 rm -f
echo "==> del map \n"

cd ..

rm -f files.tar.gz

tar -zcf files.tar.gz -C ./files .
echo "==> compress files.tar.gz \n"

rm -rf ./files

cd ..

#rm -rf release.tar.gz
mv release.tar.gz release-bak.tar.gz

tar -zcf release.tar.gz ./dist
echo "compress dist to release.tar.gz"

rm -rf ./dist


